import {
    After<PERSON>iew<PERSON>nit,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    NgZone,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    Renderer2,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { webrtcData } from 'app/interfaces/webrtc.interface';
import { HeaderStoreService } from 'app/services/store_servcies/header-store.service';
import { VideoExtractorComponent } from "app/shared/components/video-extractor/video-extractor.component";

import { Guid } from 'app/shared/enum/guid';
import { PlaybackDirection } from 'app/shared/enum/playback-direction.enum';
import { PTZDirection } from 'app/shared/enum/ptz-direction.enum';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import {
    DeviceStatusService
} from "app/shared/modules/data-layer/services/device-status/device-status.service";
import { DataService } from 'app/shared/services/data.service';
import { EventService, EventType } from 'app/shared/services/event.service';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { MicrophoneService } from 'app/shared/services/microphone.service';
import { NavigationService } from 'app/shared/services/navigation.service';
import { PlayersService } from 'app/shared/services/players.service';
import { SettingsService } from 'app/shared/services/settings.service';
import { SignalRService } from 'app/shared/services/signalR.service';
import { SignalingService } from 'app/shared/services/signaling.service';
import { TimeService } from 'app/shared/services/time.service';
import { VideoWallService } from 'app/shared/services/video-wall.service';
import * as fromAppReducers from 'app/store/app.reducers';
import * as moment from 'moment';
import { MessageService } from 'primeng/api';
import { Observable, Subscription } from 'rxjs';
import { debounceTime, finalize, take } from 'rxjs/operators';
import { AppModal } from '../app-modal/app-modal.component';
import { PlayerConfig } from './player-config.interface';
import {ApiCommands,Directions,GuidUtils} from "app/shared/enum/enum";
import { AuthService } from '../../services/auth.service';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare let SimpleWebRTC: any;

@Component({
    selector:'app-player',
    templateUrl:'./player.component.html',
    styleUrls:['./player.component.scss']
})
export class PlayerComponent implements OnDestroy,OnChanges,OnInit,AfterViewInit {

    NORMALIZE_CONST: number = 65535;
    @ViewChild('VCContainer',{static:false}) videoContainer: ElementRef;
    @ViewChild('wrapperPlayer',{static:false}) wrapperPlayer: ElementRef;
    @ViewChild('videoExtractor',{static:false}) private videoExtractor: VideoExtractorComponent;
    @ViewChild('customContextMenu') customContextMenu: ElementRef;
    @ViewChild('VCVideo',{static:false}) videoEmpty: ElementRef;
    @ViewChild('vcaConfig',{static:false}) vcaConfigCanvas: ElementRef;
    @ViewChild('vcaEvents',{static:false}) vcaEventsCanvas: ElementRef;
    @ViewChild('time',{static:false}) timeElement: ElementRef;
    vcaConfigContext: CanvasRenderingContext2D;
    vcaCountersObj: Record<string,any> = {};
    vcaCounters: any[] = [];
    vcaOffsetX: number = 0;
    vcaOffsetY: number = 0;
    showVCAObjects: boolean;
    showVCAConfig: boolean;
    vcaInited: boolean = false;
    @Output() CloseChannelEvent: EventEmitter<string> = new EventEmitter();
    @Output() OpenChannelEvent: EventEmitter<string> = new EventEmitter();
    isPTZ: boolean;
    isFrozen: boolean;
    posterURL: string = '/assets/images/vertical-logo-blue.png';

    playbackSpeed: number;
    @Input("showPlayerActions") showPlayerActions: boolean = true;
    webrtc: any;
    vidElement: HTMLVideoElement;
    errorMode: boolean = false;
    errorMsg: string;
    serverId: string;
    clickWait: boolean = false;
    isInDashboard: boolean = false;
    disableNotifyService: boolean;
    signalRconnectionRes: any;
    videoTimestamp: moment.Moment;
    isSmall: boolean;
    vcaConfig: any;
    ignoreClicks: boolean = false;
    playerState: any;
    playbackTimeError: string = "";
    @Input() isFromPopUpContainer: boolean = false;
    @Input() allowClose: boolean = true;
    groupedCamerasStatuses: {
        parent: string,
        data: { name: string,status: string },
        children: {
            data: { id: string,name: string,serverId: string,showSpinner: boolean,state: string,stateOnline: boolean }
        }[]
    }[] = [];
    isOpenFullScreen: boolean = false;
    sub: Subscription[] = [];
    draggedChannelId: Guid;
    @Input() playerConfig: PlayerConfig = null;
    peerId: string;
    public micListening: boolean;
    private audioFrameReceivedSubscription: Subscription;
    public canOpenMic: boolean = true;
    public audioOutEnabled: boolean;
    @ViewChild('modal',{static:false}) modal: AppModal;
    public PTZDirection: PTZDirection;
    public playbackDirection: typeof PlaybackDirection = PlaybackDirection;
    public subscriptions: Subscription[] = [];
    queryParam: ActivatedRoute;
    isChannelTour: boolean;
    showVolume: boolean = false;
    sliderLevel: number = 0;
    playerId: string;
    playerIndex: string;
    channelId: string;
    channelName: string;
    isActive: boolean;
    showSpinner: boolean;
    fromTime: Date = new Date();
    isLive: boolean = false;
    playbackIsRunningInPopup: boolean = false;
    updateLocalStorage: boolean = true; // Flag to control local storage updates
    @Output() onContainerClick = new EventEmitter<void>();

    constructor(
        private dataService: DataService,
        private i18n: TranslateService,
        private playersService: PlayersService,
        private signalrService: SignalRService,
        private timeService: TimeService,
        private store: Store<fromAppReducers.AppState>,
        private navigationService: NavigationService,
        private eventService: EventService,
        private zone: NgZone,
        private renderer: Renderer2,
        private micService: MicrophoneService,
        private messageService: MessageService,
        private videoWallService: VideoWallService,
        private localStorageService: LocalStorageService,
        private settingsService: SettingsService,
        private route: ActivatedRoute,
        private headerStoreService: HeaderStoreService,
        private signalingService: SignalingService,
        private deviceStatusService: DeviceStatusService,
        private authService: AuthService,
    ) {
        this.signalingService.checkSignalingServer().then((signalingServerOnline: boolean) => {
            if (!signalingServerOnline){
                this.messageService.add({
                    severity:"error",
                    summary:this.i18n.instant(ToastTypes.error),
                    detail:this.i18n.instant('signalingServerDown')
                })
            }
        });

        let openChannelPlaybackAPISubject = this.playersService.openChannelPlaybackAPISubject.subscribe(data=>{
            if (data.playerId == this.playerId) {

                this.openChannelPlaybackAPI(data.channelId,data.speed,data.time).subscribe();
            }

        });
        let openChannelSubject = this.playersService.openChannelSubject.subscribe(data=>{
            this.openChannel(data.id,data.isChannelTour,data.playerId,data.updateLocalStorage);
        });


        let openCameraChannelSubject = this.playersService.openCameraChannelSubject.subscribe(data=>{
            // this.openCameraChannel(data);
        });


        this.playersService.setBackToLiveSubject.subscribe(data=>{
            this.backToLiveAction(data.playerId);
        });
        this.subscriptions.push(openChannelPlaybackAPISubject,openChannelSubject);

        this.subscriptions.push(
            this.playersService.PlaybackTimeChanged.subscribe(data => {
                if (data.playerId === this.playerId) {
                    this.onPlaybackTimeChange(data.time);

                    if (this.isFromPopUpContainer && !this.playbackIsRunningInPopup) 
                    {
                        this.playbackIsRunningInPopup = true;
                        this.playbackTimeSelect();
                    }
                }
            })
        );

        this.signalrService.GetConnection('VideoHub').pipe(
            take(1))
            .subscribe(res=>{
                this.signalRconnectionRes = res;
            });

        this.playerId = GuidUtils.newGuid();
        this.initData();

        this.route.queryParams.subscribe(params=>{
            this.isChannelTour = params['isChannelTour'] === undefined || params['isChannelTour'].toLowerCase() === 'false' ? false : true;
            this.channelId = params['channel'];

            if (this.channelId && !this.isChannelTour) {
                this.openChannel(this.channelId,false,this.playerId);
            }
        });
    }

    ngAfterViewInit(): void {
        let playerBackgroundImage = this.localStorageService.get(LocalStorageService.PlayerBackgroundImage);
        if (playerBackgroundImage) {
            let nativeElement = this.wrapperPlayer.nativeElement;
            this.renderer.setStyle(nativeElement,'background-image','url(' + playerBackgroundImage + ')');
            this.renderer.setStyle(nativeElement,'background-repeat','no-repeat');
            this.renderer.setStyle(nativeElement,'background-size','cover');
        }


        this.renderer.listen(this.wrapperPlayer.nativeElement,'contextmenu',(event)=>{
            event.preventDefault();
            this.showContextMenu(event);


        });

        this.renderer.listen('window','click',(event)=>{
            this.customContextMenu.nativeElement.style.display = 'none';
        });
    }

    showVideoExtractor() {
        this.videoExtractor.show(this.channelId,this.channelName);
    }

    showContextMenu(event: MouseEvent) {
        this.customContextMenu.nativeElement.style.display = 'block';
        this.customContextMenu.nativeElement.style.top = event.clientY - 80 + 'px';
        this.customContextMenu.nativeElement.style.left = event.clientX - 300 + 'px';
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.playerConfig && changes.playerConfig.currentValue) {
            this.playerConfig = changes.playerConfig.currentValue;
            this.openPlayer(changes.playerConfig.currentValue);
        }
    }

    ngOnInit(): void {
        if (this.isFromPopUpContainer) {
            this.showSpinner = true;
        }

        let sub = this.store.select(fromAppReducers.StoreNames.getUserResourceGroupsWithChannel).subscribe(res=>{
            if (res && res.length > 0) {
                this.groupedCamerasStatuses = res[1];
            }
        });
        this.sub.push(sub);
        sub = this.headerStoreService.getState().subscribe(settings=>{
            if (!this.playerConfig) {
                this.showVCAConfig = settings.vcaOptions.showConfig;
                this.showVCAObjects = settings.vcaOptions.showObjects;
            }
        });

        this.sub.push(sub);
        sub = this.eventService.observe(EventType.ViewPortResize).pipe(debounceTime(200)).subscribe(()=>{
            return this.sizeChanged();
        });
        this.sub.push(sub);
        sub = this.navigationService.getParams().subscribe(()=>{
            return this.vcaInited = false;
        });
        this.sub.push(sub);
        let parsedChannelId = this.channelId ? Guid.parse(this.channelId) : "";
        sub = this.eventService.observe(EventType.VCAConfig).subscribe((inputChannelId: Guid)=>{


            if (parsedChannelId === inputChannelId) {
                this.reopenChannel();
            }
        });
        this.sub.push(sub);
        sub = this.deviceStatusService.resourceChanged.subscribe(resource=>{

            if (resource.type !== DataChangeType.Update && resource.type !== DataChangeType.Delete) {
                return;
            }

            let deviceStatuses = resource.models.filter(model=>{
                return model.identity === this.channelId;
            });
            if (deviceStatuses.length === 0) {
                return;
            }

            let isOnline = deviceStatuses.some(deviceStatus=>{
                return deviceStatus.status === ResourceState.online;
            });
            if (!isOnline) {
                this.closeMic();
            }
        });
        this.sub.push(sub);

        this.playersService.draggedChannelData.subscribe((channelId)=>{
            this.draggedChannelId = Guid.parse(channelId);
        });

        if (this.playerConfig && this.playerConfig.selectedChannelId !== Guid.EMPTY) {
            this.openPlayer(this.playerConfig);
        }

        this.micService.becameBusy.subscribe(()=>{
            this.canOpenMic = false;
        });

        this.micService.becameFree.subscribe(()=>{
            if (this.audioFrameReceivedSubscription) {
                this.audioFrameReceivedSubscription.unsubscribe();
                this.audioFrameReceivedSubscription = null;
            }
            this.canOpenMic = true;
        });
    }

    openPlayer(player: PlayerConfig): void {
        if (!player.selectedChannelId) {
            console.info('Refusing to open channel without a channel id');
            return;
        }
        this.showVCAObjects = player.vcaObject;
        this.showVCAConfig = player.vcaConfig;
        this.isPTZ = player.ptzControls;
        this.playersService.openChannel(player.selectedChannelId,this.playerId);
    }

    toggleFullscreen(): void {
        this.vcaInited = false;
        this.isOpenFullScreen = !this.isOpenFullScreen;
    }

    public ngOnDestroy(): void {
        if (this.audioFrameReceivedSubscription) {
            this.closeMic();
        }
        this.peerId = null;

        if (this.isActive) {
            this.closeChannel(false);
        }
        this.sub.forEach(sub=>{
            sub.unsubscribe();
        });
        this.disconnect();
        this.webrtc = null;
        this.subscriptions.map(subscription=>{
            subscription.unsubscribe();
        });
    }

    setInDashboad(isDashboard: boolean): void {
        this.isInDashboard = isDashboard;
    }

    public initData(): void {
        this.isLive = true;
        this.showSpinner = false;
        this.isActive = false;
        this.isPTZ = false;
        this.isFrozen = false;
        this.errorMode = false;
        this.channelName = "";
        this.channelId = "";
        this.serverId = "";
        this.errorMsg = "";
        this.vidElement = null;
        this.playbackSpeed = 1;
        this.disableNotifyService = false;
        this.videoTimestamp = null;
        this.vcaConfig = null;
        this.vcaInited = false;
        this.vcaCounters = [];
        this.vcaCountersObj = {};
        this.clearVCAConfig();
        this.showVCAConfig = true;
        this.showVCAObjects = true;

        this.disconnect();
        if (this.vidElement) {
            this.vidElement.srcObject = null;
            this.videoContainer.nativeElement.removeChild(this.vidElement);
        }
        if (this.webrtc) {
            this.webrtc.stopLocalVideo();
            this.webrtc.connection.disconnect();
            this.webrtc = null;
        }
    }

    public setErrorMode(enabled: boolean,msg: string): void {
        this.errorMode = enabled;
        this.errorMsg = this.errorMode ? this.i18n.instant(msg) : "";
    }

    public getPlayerId(): string {
        return this.playerId;
    }


    public initPlayer(val: webrtcData,isChannelTour?: boolean): void {
        this.showSpinner = true;

        if (!this.isValidData(val)) {
            this.initData();
            this.messageService.add({
                severity:'success',
                summary:this.i18n.instant(ToastTypes.error),
                detail:this.i18n.instant('failedToOpenChannel')
            });
            return;
        }

        this.isFrozen = false;
        this.channelId = val.channelId;
        this.serverId = val.serverId;
        this.isPTZ = val.hasPTZ;
        this.channelName = val.name;
        this.vcaConfig = val.vcaConfig;
        this.isLive = true;
        let room = val.room;
        let targetId = val.id;
        let signaling = val.signaling;
        this.isSmall = this.videoContainer.nativeElement.clientWidth < 350;
        this.playerState = this.vcaConfig ? "blue" : this.getStateCameraColor();
        this.audioOutEnabled = val.audioOutEnabled;
        this.webrtc = new SimpleWebRTC({
            target:targetId,
            url:signaling,
            iceServers:[{'urls':'stun:stun.l.google.com:19302'}],
            localVideoEl:'',
            remoteVideosEl:'',
            autoRequestMedia:false,
            debug:false,
            detectSpeakingEvents:false,
            autoAdjustMic:false
        });
        let self = this;
        this.playersService.setPlayersRef([self]);
        this.webrtc.on('readyToCall',()=>{
            if (this.vidElement !== null) {
                console.log("reopen fix");
                this.reopenChannel();
            } else if (room && this.webrtc) {
                this.webrtc.setInfo('',this.webrtc.connection.connection.id,''); // Store strongId
                this.webrtc.joinRoom(room);
            } else {
                this.initData();
                console.log("playerComponent._webrtc.on.'readyToCall'_failed",room,this.webrtc);
            }
        });

        this.webrtc.on('videoAdded',(video,peer)=>{
            if (peer.id === targetId || peer.strongID === targetId || peer.nickName === targetId && this.webrtc !== null) {
                this.notifyPlayersService(true,true,isChannelTour);
                this.isActive = true;
                this.showSpinner = false;
                this.zone.run(()=>{ //wait for change detection hides the "add camera" place holder
                    video.classList.add("cymbiot-player");
                    if (this.vidElement) {
                        this.videoContainer.nativeElement.removeChild(this.vidElement);
                    }
                    this.videoContainer.nativeElement.prepend(video);
                    this.vidElement = video;
                    this.vidElement.volume = 0.5;
                    this.sliderLevel = this.vidElement.volume * 100;

                    this.webrtc.stopLocalVideo();
                    this.allowClose = true;
                    let promise = video.play();
                    if (promise !== undefined) {
                        promise.then(()=>{
                            console.info('video play success;');
                        }).catch(error=>{
                            //When running playback in popup video player we switch from live to playback 
                            //so we receive from WebRTC: AbortError, code: 20, message: "The play() request was interrupted by a new load request.".
                            //This is normal and the playback is running fine, we don't show the error in the console.
                            if (!this.playbackIsRunningInPopup && error.code != 20)
                            {
                                console.error(error);
                            }
                            this.isFrozen = true;
                        });
                    }
                });
                this.peerId = peer.id;
            }
        });

        this.webrtc.on('channelMessage',(peer,label,data)=>{
            if (data.type === 'custommessage') {
                if (data && data.payload && data.payload === "errorCountMaxed") {
                    console.log("Error Count Maxed Suspected");
                } else if (data && data.payload) {
                    let dataChannelInfo = this.isJSON(data.payload);
                    this.hadleNewTimestamp(dataChannelInfo.timestamp);
                    setTimeout(()=>{
                        //wait for video element to be rendered and and draw vca config
                        //TODO refactor player component
                        this.drawVCAConfig();
                    },1200);
                    if (this.vcaConfig && this.vcaInited) {
                        this.drawVCAEVents(this.isJSON(dataChannelInfo.vcaData));
                    }
                } else {
                    let res = {"channelId":this.channelId,"channelName":this.channelName,"payload":data.payload};
                    this.webrtc.sendDataChannelMessageToPeer(data.from,JSON.stringify(res));
                }
            }
        });

        this.webrtc.on('videoRemoved',(video,peer)=>{
            if (this.isActive) {
                console.log("WebRTC Error",peer);
                this.setErrorMode(true,"noStreamerConnection");
                this.playersService.setStremerError(this);
            }
        });
    }

    private sizeChanged() {
        if (this.isActive) {
            this.vcaInited = false;
        }
    }

    private hadleNewTimestamp(timestamp) {
        if (timestamp) {
            let tmp = this.timeService.GetMomentObjFromStr(timestamp,null);
            if (tmp.isSameOrBefore(this.videoTimestamp) && this.videoTimestamp && this.isLive) {
                return; //timestamp didnt change
            }

            this.videoTimestamp = tmp;
            //dont use angular change detection to update timestamp (improve preformance)
            this.zone.runOutsideAngular(()=>{
                setTimeout(()=>{
                    if (this.timeElement) {
                        this.renderer.setProperty(this.timeElement.nativeElement,'textContent',this.GetTimeStamp());
                    }
                },0);
            });

            if (!this.isLive) {
                this.playersService.setPlaybackTime(this.playerId,this.videoTimestamp.toDate());
            }
        }
    }

    private isJSON(str) {
        try {
            return JSON.parse(str);
        } catch (e) {
            return "";
        }
    }

    public reopenChannel(): void {
        this.disableNotifyService = true;
        let channelId = this.channelId;
        this.closeChannel();
        setTimeout(()=>{
            this.playersService.openChannel(channelId,this.playerId);
        },500);
    }

    private notifyPlayersService(isOpen: boolean, isClearSettings, isChannelTour?: boolean): void {
        if (this.disableNotifyService) {
            return;
        }
        if (isOpen) {
            this.playersService.setChannelOpend(this.playerId, isChannelTour, this.channelId, this.updateLocalStorage);
            this.OpenChannelEvent.emit(this.channelId);
        } else {
            this.playersService.setChannelClosed(this,isClearSettings);
        }
    }

    public closeBtnClicked(): void {
        this.closeChannel();
        this.settingsService.set('channel_tour_player_' + this.playerIndex,'');
        this.playersService.closeChannelTour.next(this);

    }

    public closeChannel(isClearSettings: boolean = true,isCloseModal: boolean = true): void {
        if (isCloseModal) {
            /*close the popup if exists*/
            this.CloseChannelEvent.emit(this.channelId);
        }
        this.playersService.removePlayer(this.channelId);
        this.videoWallService.closePlayerSubject.next(this.playerId);
        this.notifyPlayersService(false,isClearSettings);
        this.closeStream();
    }

    public GetTimeStamp(): string {
        let x: string;
        if (this.videoTimestamp) {
            x = this.isSmall ? this.timeService.GetTime(this.videoTimestamp) : this.timeService.GetUserString(this.videoTimestamp);
        } else {
            x = "";
        }
        return x;
    }

    private closeStream() {
        if (!this.isActive) {
            return;
        }
        if (this.vidElement) {
            try {
                this.vidElement.srcObject = null;
            } catch {
                console.error('Couldn\'t close stream');
            }
            try {
                this.videoContainer.nativeElement.removeChild(this.vidElement);
            } catch {
                console.error('Couldn\'t remove video element');
            }
        }
        this.initData();
    }

    public movePTZ(dir: PTZDirection): void {
        let direction;
        switch (dir) {
            case PTZDirection.UP: {
                direction = Directions.Top;
                break;
            }
            case PTZDirection.RIGHT: {
                direction = Directions.Right;
                break;
            }
            case PTZDirection.DOWN: {
                direction = Directions.Bottom;
                break;
            }
            case PTZDirection.LEFT: {
                direction = Directions.Left;
                break;
            }
            default: {
                console.info("Moved PTZ default");
                break;
            }
        }

        this.signalRconnectionRes.invoke('VideoPTZMove',this.channelId,direction,2,2);
    }

    public zoomPTZ(dir: string): void {
        this.signalRconnectionRes.invoke('VideoPTZZoom',this.channelId,dir === "in" ? 1 : 0,2);
    }

    public stopPTZ(): void {
        this.signalRconnectionRes.invoke('VideoPTZStop',this.channelId);
    }

    public playback(dir: PlaybackDirection): void {
        if (dir === PlaybackDirection.FastForward) {
            if (this.isLive) {
                return;
            }

            if (this.playbackSpeed >= 1) {
                if (this.playbackSpeed < 16) {
                    this.playbackSpeed *= 2;
                } else {
                    this.playbackSpeed = 2;
                }
            } else {
                this.playbackSpeed = 1;
            }

        } else if (dir === PlaybackDirection.FastBackward) {
            if (this.isLive) {
                return;
            }

            if (this.playbackSpeed <= -1) {
                if (this.playbackSpeed > -16) {
                    this.playbackSpeed *= 2;
                } else {
                    this.playbackSpeed = -2;
                }
            } else {
                this.playbackSpeed = -1;
            }
        }

        this.isFrozen = false;
        this.signalRconnectionRes.invoke('OpenChannelPlayback',this.channelId,null,Math.abs(this.playbackSpeed),this.playbackSpeed > 0 ? 0 : 1,this.playerId);
    }

    public backToLive($event: MouseEvent): void {
        if ($event) {
            $event.stopPropagation();
        }

        let channel = this.channelId;
        this.closeChannel();
        setTimeout(()=>{
            this.openChannel(channel);
        },1000);
    }

    public disconnect(): void {
        if (this.webrtc) {
            this.webrtc.leaveRoom();
        }
    }

    public playPause(isPlay: boolean): void {
        if (this.isLive) {
            this.isFrozen ? this.vidElement.play() : this.vidElement.pause();
            this.isFrozen = this.vidElement.paused;
        } else {
            this.isFrozen = !this.isFrozen;
            this.signalRconnectionRes.invoke('PlayPause',isPlay,this.channelId,this.playerId);
        }
    }

    public openMic(): void {
        if (this.audioFrameReceivedSubscription) {
            throw 'There is already an audio frame receive subscription';
        }

        this.audioFrameReceivedSubscription = this.micService.audioFrameReceived.subscribe((audioFrameAsString)=>{
            if (!this.webrtc) {
                this.closeMic();
                return;
            }

            this.webrtc.sendDataChannelMessageToPeer(this.peerId,"AudioFrame_" + audioFrameAsString);
        });

        this.micService.listeningStarted
            .pipe(take(1))
            .subscribe(()=>{
                this.micListening = true;
            });

        if (!this.micService.startListening()) {
            this.messageService.add({
                severity:'error',
                summary:this.i18n.instant('outdatedBrowserTitle'),
                detail:this.i18n.instant('outdatedBrowser')
            });
        }
    }

    public closeMic(): void {
        if (!this.audioFrameReceivedSubscription) {
            return;
        }

        this.audioFrameReceivedSubscription.unsubscribe();
        this.audioFrameReceivedSubscription = null;

        this.micService.stopListening();
        this.micListening = false;
        if (this.webrtc) {
            this.webrtc.sendDataChannelMessageToPeer(this.peerId,"StopAudio");
        }
    }

    resetVCACounter(): void {
        if (this.ignoreClicks) {
            return;
        }
        this.ignoreClicks = true;
        this.dataService.api({
            type:ApiCommands.ResetCounter,
            disableErrorHandler:true,
            disableBI:true,
            urlParams:this.channelId
        }).pipe(take(1),finalize(()=>{
            this.ignoreClicks = false;
        })).subscribe();
    }

    private movePTZAPI(dir) {
        this.dataService.api({
            type:ApiCommands.MovePTZ,
            disableErrorHandler:true,
            disableBI:true,
            data:{
                channel:this.channelId,
                direction:dir,
                panSpeed:2,
                tiltSpeed:2,
            }
        }).pipe(take(1)).subscribe();
    }

    private zoomPTZAPI(isZoomIn) {
        this.dataService.api({
            type:ApiCommands.ZoomPTZ,
            disableErrorHandler:true,
            disableBI:true,
            data:{
                channel:this.channelId,
                isZoomIn:isZoomIn,
                zoomSpeed:2
            }
        }).pipe(take(1)).subscribe();
    }

    private stopPTZAPI() {
        this.dataService.api({
            type:ApiCommands.StopPTZ,
            disableErrorHandler:true,
            disableBI:true,
            urlParams:this.channelId
        }).pipe(take(1)).subscribe();
    }

    private isValidData(input: webrtcData): boolean {
        let isValid = false;
        if (input && input.signaling && input.room && input.id) {
            isValid = true;
        }
        return isValid;
    }

    public openChannelPlaybackAPI(channelId: string,speed: number,time: Date,isCloseModal = true): Observable<void> {

        return Observable.create(observer=>{
            this.channelId = channelId;
            this.playbackSpeed = speed;
            this.fromTime = time;
            this.dataService.api({
                type:ApiCommands.VideoPlayback,
                disableErrorHandler:true,
                disableBI:true,
                data:{
                    channel:channelId
                    ,datetime:time ? time.toISOString() : ""
                    ,rate:speed
                    ,player:this.playerId
                }
            }).pipe(take(1)).subscribe(
                res=>{
                    this.isFrozen = false;
                    if (this.isLive) {
                        this.disableNotifyService = true;
                        this.closeChannel(true,isCloseModal);
                        this.showSpinner = false;

                        setTimeout(()=>{

                            this.initPlayer(res);
                            this.isLive = false;
                            this.showSpinner = false;
                            observer.next("fromLive");
                            observer.complete();
                        },1000);
                    } else {
                        observer.next("done");
                        observer.complete();
                        this.playPause(false);
                    }
                    this.playersService.setPlaybackTime(this.playerId,this.fromTime);

                },err=>{

                    this.messageService.add({
                        severity:'error',
                        summary:this.i18n.instant(ToastTypes.error),
                        detail:this.i18n.instant('openPlaybackFailed')
                    });
                    this.closeChannel();
                    observer.next(err);
                    observer.complete();
                });

        });
    }

    public openChannel(id: string,isChannelTour?: boolean,playerId?: string, updateLocalStorage: boolean = true): void {
        if (playerId != this.playerId) {
            return;
        }

        if (!id) {
            console.error("no channel id was provided!");
            this.initData();
            return;
        }

        this.channelId = id;
        this.updateLocalStorage = updateLocalStorage; // Set the flag for local storage updates

        this.playersService.openChannelAPI(id).subscribe(
            res=>{
                let timeout = 0;
                if (this.isActive) {
                    this.closeChannel();
                    timeout = 1000;
                }

                setTimeout(()=>{
                    this.initPlayer(res, isChannelTour);
                },timeout);

            },
            (err)=>{
                let errorMessage = '';
                switch (err.status) {
                    case 503:
                        errorMessage = 'openChannelFail503';
                        break;
                    case 502:
                        switch (err.message) {
                            case 'StreamNotSupported':
                                errorMessage = 'StreamNotSupported';
                                break;
                            case 'FailedToConnect':
                                errorMessage = 'FailedToConnect';
                                break;
                            default:
                                errorMessage = 'openChannelFail';
                                break;
                        }
                        break;
                    default:
                        errorMessage = 'openChannelFail';
                        break;
                }
                this.clearOnError(errorMessage);
            });

    }

    clearOnError(message: string): void {
        this.messageService.add({
            severity:'error',
            summary:this.i18n.instant(ToastTypes.error),
            detail:this.i18n.instant(message)
        });
        this.playersService.removePlayer(this.channelId);
        this.allowClose = false;
        this.showSpinner = false;
    }

    closePopUp(): void {
        this.CloseChannelEvent.emit(this.channelId);
    }

    openModal(type: string): void {
        if (type === "playback") {
            this.modal.openModal();
            this.fromTime = this.videoTimestamp.toDate();
        } else if (!this.isActive) {
            this.playersService.openVideoChannelDialog(this.playerId);
        }
    }

    openVideoChannels(): void {
        if (!this.isActive) {
            this.onContainerClick.emit();
        }
    }

    onCancel($event: MouseEvent): void {
        if ($event) {
            $event.stopPropagation();
        }
        this.modal.closeModal();
    }

    onPlaybackTimeChange(event: Date): void {
        let from = moment(event);
        if (from > moment()) {
            this.playbackTimeError = this.i18n.instant("dateOverflow");
        } else {
            this.playbackTimeError = "";
            this.fromTime = event;
        }
    }

    playbackTimeSelect(): void {
        if (this.playbackTimeError !== "") {
            return;
        }
        if (!this.fromTime) {
            this.messageService.add({
                severity:'error',
                summary:this.i18n.instant(ToastTypes.error),
                detail:this.i18n.instant('dateIsRequired')
            });
            return;
        }

        this.openChannelPlaybackAPI(this.channelId,1,this.fromTime,false).pipe(take(1)).subscribe();
        this.modal.closeModal();
    }

    getStateCameraColor(): string {
        let groupedCameras = this.groupedCamerasStatuses;
        if (groupedCameras) {
            for (let index = 0; index < groupedCameras.length; index++) {
                for (let z = 0; z < groupedCameras[index].children.length; z++) {
                    let item = groupedCameras[index].children[z];
                    if (item.data.id === this.channelId) {
                        return item.data.state;
                    }
                }
            }
        }
    }

    private clearVCAConfig() {
        if (this.vcaConfigContext && this.vcaConfigCanvas) {
            this.vcaConfigContext.clearRect(0,0,this.vcaConfigCanvas.nativeElement.width,this.vcaConfigCanvas.nativeElement.height);
        }
    }

    private initVCACanvas() {
        if (this.vcaConfigCanvas) {
            let canvas = this.vcaConfigCanvas.nativeElement;
            this.vcaConfigContext = canvas.getContext("2d");
            this.vcaConfigContext.canvas.width = canvas.clientWidth;
            this.vcaConfigContext.canvas.height = canvas.clientHeight;
            this.clearVCAConfig();
        }

    }

    changeShowVolume() {
        this.showVolume = !this.showVolume;
    }

    private getColor(input: string) {
        let s = parseInt(input).toString(16);
        while (s.length < 6) {
            s = "0" + s;
        }
        return "#" + s;
    }

    private drawVCAConfig() {
        if (!this.vcaConfig || this.vcaInited) {
            return;
        }
        if (!this.calcVideoOffset()) {
            return;
        }
        this.initVCACanvas();
        let vca = JSON.parse(this.vcaConfig);
        this.itemOrArrayToArray(vca["Root"]["ZoneSettings"]["Zone"]).forEach(zone=>{
            if (zone) {
                let color = this.getColor(zone["@Color"]);
                this.drawLines(zone["Point"],color,this.vcaConfigContext,"@X","@Y");
            }
        });
        this.itemOrArrayToArray(vca["Root"]["CounterSettings"]["Counter"]).forEach(counter=>{
            if (!counter) {
                return;
            }
            let element = counter["Point"][0];
            let noraml = this.normalizeVCAPoint(element["@X"],element["@Y"]);
            element = counter["Point"][1];
            let noraml1 = this.normalizeVCAPoint(element["@X"],element["@Y"]);
            element = counter["Point"][2];
            let noraml2 = this.normalizeVCAPoint(element["@X"],element["@Y"]);
            let top = noraml.y.toString() + "px";
            let left = noraml.x.toString() + "px";
            let width = (noraml1.x - noraml.x).toString() + "px";
            let height = (noraml2.y - noraml.y).toString() + "px";
            this.vcaCountersObj[counter["@Id"]] =
                {
                    "name":counter["@Name"],
                    "color":this.getColor(counter["@Color"]),
                    "position":{"top":top,"left":left,"width":width,"height":height},
                    "value":0
                };
            this.vcaCounters = [];
            for (let key in this.vcaCountersObj) {
                this.vcaCounters.push({key:key,value:this.vcaCountersObj[key]});
            }
        });
    }

    private drawVCAEVents(events) {
        if (!events || this.isFrozen) {
            return;
        }
        let canvas = this.vcaEventsCanvas.nativeElement;
        let vcaEvetnsContext = canvas.getContext("2d");
        vcaEvetnsContext.canvas.width = canvas.clientWidth;
        vcaEvetnsContext.canvas.height = canvas.clientHeight;
        if (this.showVCAObjects) {
            if (events["vca"]["objects"] && events["vca"]["objects"]["object"] !== "") {
                this.drawBoundaryBox(events["vca"]["objects"]["object"],"yellow",vcaEvetnsContext);
            }
        }
        if (events["vca"]["events"] && events["vca"]["events"]["event"] !== "") {
            this.drawBoundaryBox(events["vca"]["events"]["event"],"red",vcaEvetnsContext);
        }
        if (events["vca"]["counts"] && events["vca"]["counts"]["count"] !== "") {
            this.itemOrArrayToArray(events["vca"]["counts"]["count"]).forEach(counter=>{
                this.vcaCountersObj[counter["id"]].value = counter["val"];
            });
        }
    }

    private drawBoundaryBox(points,color,context) {
        if (!points || !context) {
            return;
        }
        this.itemOrArrayToArray(points).every((object)=>{
            if (!object) {
                return false;
            }
            let bb = object["bb"];
            if (bb) {
                let noramlxy = this.normalizeVCAPoint(bb["x"],bb["y"]);
                let noramlwh = this.normalizeVCAPoint(bb["w"],bb["h"]);
                context.rect(noramlxy.x - (noramlwh.x / 2),noramlxy.y - (noramlwh.y / 2),noramlwh.x,noramlwh.y);
                context.strokeStyle = color;
                context.stroke();
            }
            if (!object["trail"]) {
                return true;
            }
            let pts = object["trail"]["pt"];
            if (pts) {
                this.drawLines(pts,color,context);
            }
            return true;
        });
    }

    private drawLines(points,color,context,x = "x",y = "y") {
        if (!points || !context) {
            return;
        }
        this.itemOrArrayToArray(points).forEach((element,i)=>{
            let noraml = this.normalizeVCAPoint(element[x],element[y]);
            if (i === 0) {
                context.beginPath();
                context.moveTo(noraml.x,noraml.y);
            } else {
                context.lineTo(noraml.x,noraml.y);
            }

        });
        context.lineWidth = 2;
        context.strokeStyle = color;
        context.stroke();
    }

    private itemOrArrayToArray(input) {
        if (input instanceof Array) {
            return input;
        } else {
            return [input];
        }
    }

    private normalizeVCAPoint(x,y) {
        let normalX = (x / this.NORMALIZE_CONST) * this.vcaOffsetX + (this.vcaConfigCanvas.nativeElement.clientWidth - this.vcaOffsetX) / 2;
        let normalY = (y / this.NORMALIZE_CONST) * this.vcaOffsetY + (this.vcaConfigCanvas.nativeElement.clientHeight - this.vcaOffsetY) / 2;
        let val = {"x":Math.round(normalX),"y":Math.round(normalY)};
        return val;
    }

    private calcVideoOffset() {
        let res = this.vidElement.videoHeight / this.vidElement.videoWidth;
        if (!res && !this.vcaConfigCanvas) {
            this.vcaInited = false;
        } else {
            this.vcaOffsetX = this.vcaConfigCanvas.nativeElement.clientWidth;
            this.vcaOffsetY = this.vcaConfigCanvas.nativeElement.clientHeight;
            if (res < 9 / 16) {
                this.vcaOffsetY = this.vcaConfigCanvas.nativeElement.clientWidth * res;
            } else if (res > 9 / 16) {
                this.vcaOffsetX = this.vcaConfigCanvas.nativeElement.clientHeight * 1 / res;
            }
            this.vcaInited = true;
        }
        return this.vcaInited;

    }

    onDropEndChannel(): void {
        let nativeElement = this.wrapperPlayer.nativeElement;
        if (this.draggedChannelId) {
            this.playersService.openChannel(this.draggedChannelId.toString(),this.playerId);
        }
        this.renderer.removeClass(nativeElement,'ondropelement');
    }

    onDropEnterChannel(): void {
        let nativeElement = this.wrapperPlayer.nativeElement;
        this.renderer.addClass(nativeElement,'ondropelement');
    }

    onDropLeaveChannel(): void {
        let nativeElement = this.wrapperPlayer.nativeElement;
        this.renderer.removeClass(nativeElement,'ondropelement');
    }

    backToLiveAction(playerId: string): void {
        if (playerId == this.playerId) {
            this.backToLive(null);
        }
    }

    changeVolumeEvent(event) {
        this.vidElement.volume = event / 100;
        this.sliderLevel = event;

    }

    takeSnapshot(): void {
        const formats = {
            png: {
                mimeType: "image/png",
                extension: "png"
            },
            tif: {
                mimeType: "image/tiff",
                extension: "tif"
            },
            gif: {
                mimeType: "image/gif",
                extension: "gif"
            },
            jpg: {
                mimeType: "image/jpeg",
                extension: "jpg"
            },
            jpeg: {
                mimeType: "image/jpeg",
                extension: "jpeg"
            }
        };
    
        const defaultFormat = formats.png;
    
        if (this.vidElement) {
            const canvas = document.createElement("canvas");
            canvas.width = this.vidElement.videoWidth;
            canvas.height = this.vidElement.videoHeight;
            const context = canvas.getContext("2d");
            if (!context) {
                console.error("Failed to get canvas context.");
                return;
            }
    
            context.drawImage(this.vidElement, 0, 0, canvas.width, canvas.height);
            const userLabel = this.i18n.instant('watermark.user');
            const dateLabel = this.i18n.instant('watermark.date');


            context.fillStyle = "rgba(0, 0, 0, 0.3)";
            context.fillRect(0, 0, canvas.width, canvas.height);
            
            const username = this.authService.getCurrentUser().Username;
            const timestamp = new Date().toLocaleString();
            const watermarkText = `${userLabel}: ${username} - ${dateLabel}: ${timestamp}`;

            const padding = 10;
            const fontSize = 30;
            context.font = `${fontSize}px Arial`;
            context.fillStyle = "green";
            context.textAlign = "right";
            context.textBaseline = "bottom";
            context.fillText(watermarkText, canvas.width - padding - 10, canvas.height - padding - 10);

            canvas.toBlob((blob) => {
                if (blob) {
                    if ("showSaveFilePicker" in window) {
                        (window as any).showSaveFilePicker({
                            suggestedName: `Snapshot - ${this.channelName}.${defaultFormat.extension}`,
                            types: [
                                {
                                    description: "PNG Files",
                                    accept: { "image/png": [".png"] }
                                },
                                {
                                    description: "TIFF Files",
                                    accept: { "image/tiff": [".tif"] }
                                },
                                {
                                    description: "GIF Files",
                                    accept: { "image/gif": [".gif"] }
                                },
                                {
                                    description: "JPG Files",
                                    accept: { "image/jpeg": [".jpg"] }
                                },
                                {
                                    description: "JPEG Files",
                                    accept: { "image/jpeg": [".jpeg"] }
                                }
                            ],
                        }).then(handle => {
                            return handle.createWritable().then(writableStream => {
                                writableStream.write(blob);
                                return writableStream.close();
                            });
                        }).catch(error => {
                            console.error("Error saving file:", error);
                        });
                    } else {
                        // Fallback for browsers that do not support showSaveFilePicker
                        const link = document.createElement("a");
                        link.href = URL.createObjectURL(blob);
                        link.download = `Snapshot - ${this.channelName}.${defaultFormat.extension}`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                    }
                } else {
                    console.error("Failed to create blob from canvas");
                }
            }, defaultFormat.mimeType);
        } else {
            console.error("Video element is not available");
        }
    }
}