import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, QueryList, SimpleChanges, ViewChildren } from '@angular/core';
import { VideoDeviceGroup } from 'app/shared/models/video-device-group';
import { ServerTypes } from 'app/shared/modules/data-layer/enum/resource/resource-server-types.enum';
import { ResourceState } from 'app/shared/modules/data-layer/enum/resource/resource-state.enum';
import { DataChangeType } from 'app/shared/modules/data-layer/models/data-change-type.enum';
import { Resource } from 'app/shared/modules/data-layer/models/resource';
import { ResourceGroup } from 'app/shared/modules/data-layer/models/resource-group';
import { PlayersService } from 'app/shared/services/players.service';
import { CameraSelectionOptions } from 'app/shared/models/camera-selection.model';
import * as _ from 'lodash';
import { EMPTY, Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntil<PERSON>hanged, switchMap } from 'rxjs/operators';
import { CymsidebarService } from '../cym-sidebar/cym-sidebar.service';

import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ResourceService } from "app/services/resource/resource.service";
import { Guid } from 'app/shared/enum/guid';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { URI } from 'app/shared/enum/uri';
import { DataEntry } from 'app/shared/modules/data-layer/models/dataentry.model';
import { Entry } from 'app/shared/modules/data-layer/models/entry.model';
import { ChannelTourService } from 'app/shared/modules/data-layer/services/channel-tour/channel-tour.service';
import { ResourcePort } from "app/shared/modules/data-layer/services/resource/resource-port";
import { ResourceCacheService } from "app/shared/modules/data-layer/services/resource/resource.cache.service";
import { VideoWallService } from 'app/shared/services/video-wall.service';
import { MessageService } from 'primeng/api';
import { PlayerComponent } from '../player/player.component';
import { CameraCountService } from 'app/shared/services/camera-count.service';


@Component({
  selector: 'video-channels',
  templateUrl: './video-channels.component.html',
  styleUrls: ['./video-channels.component.scss']
})
export class VideoChannelsComponent implements OnInit, OnDestroy,AfterViewInit,OnChanges {
  modelStore: Map<string, Resource> = new Map<string, Resource>();
  groupedCameras: VideoDeviceGroup[] = [];
  filteredCameras:  Map<string, Resource> = new  Map<string, Resource>();

  selectedChannel: Resource;
  counter=0;
  @Input('playerId') playerId: string;
  @Input('monitorCount') monitorCount: number;
  @Input('videoWalllayouts') videoWalllayouts: any;
  channelTourArray:DataEntry[]=[];
  showBooleanCameras: {show: boolean, displayText: string}[] = [{show: true, displayText: "online"}, {show: false, displayText: "offline"}];
  searchText: string = "";
  subscriptions: Subscription[] = [];
  @Input('showActions') showActions:boolean = true;
  @Input('sidebarAutoClose') sidebarAutoClose:boolean = true;
  showResourceGroups:boolean = false;
  showChannelTours:boolean = false;
  @Output() onChannelSelect: EventEmitter<{playerId: string, channelId: string, widgetId?: string}> = new EventEmitter();
  groupsCounter: {[id: string] : number};
  @ViewChildren('cameraElements') cameraElements:QueryList<ElementRef>
  searchTerm$ = new Subject<string>();
  @Input('selectedChannelId') selectedChannelId: string;
  @Input('selectedWidgetId') selectedWidgetId: string;
  @Input('videoWallGuid') videoWallGuid: string;
  channelTours:URI[]=[];
  @Output() playerIndexChanged: EventEmitter<number> = new EventEmitter();
  @Output() viewLayout: EventEmitter<number> = new EventEmitter();
  queryParam:number;
  channelId?: Guid;
  selectedLayout;
  monitorIndex;
  channelTourMap:Map<string,Entry[]>=new Map<string,Entry[]>();
  activeChannelMap:Map<string,number>=new Map<string,number>();
  playerIdOn:Map<string,boolean>=new Map<string,boolean>();
  constructor(
    private playerService: PlayersService,
    private cymsidebarService: CymsidebarService,
    private resourceService: ResourceService,
    private videoWallService: VideoWallService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private i18n: TranslateService,
    private channelTourService: ChannelTourService,
    private resourcePort: ResourcePort,private resourceCacheService: ResourceCacheService,
    private cameraCountService: CameraCountService){

    this.playerService.playersInitialized.subscribe(playerIds => {
      this.playerId = playerIds[0].toString();
    });

   
    let searchTermSubscription = this.searchTerm$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(() => {
        this.setGroupsCounter();
        return EMPTY;
      })
    ).subscribe();
    this.subscriptions.push(searchTermSubscription);

    this.resourceService.resourceChanged.subscribe((resource: any) => {
      this.notifyOperation(resource);
  })

    this.route.queryParams.subscribe(params => {
      this.channelId=params['channel'];
   });
  }
  ngOnChanges(changes: SimpleChanges): void {
    if(changes.videoWalllayouts){
      this.selectedLayout= changes.videoWalllayouts.currentValue[0][1].id;
    }
  }
  ngAfterViewInit(): void {

  }

  ngOnInit(): void {

    this.getAllData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(element => {return element.unsubscribe();});
    this.playerIdOn.clear();
  }

  private convertResources(resources: Resource[]): Map<string, Resource>
  {
    let result = new Map<string, Resource>();
    resources.forEach(resource => result.set(resource.identity, resource));
    return result;
  }

  getAllData(): void {

      let resourcesSubscription = this.resourceCacheService.storageCompleted.subscribe(() =>{
          const res: Resource[] = this.resourceCacheService.getAll();
          this.modelStore = this.convertResources(res);

          res.forEach((resource: Resource)=>{
              if (resource.resourceType === ServerTypes.Core_RES_ChannelTour) {
                  let resourceUri = new URI([{ id:resource.identity,name:resource.name,type:resource.resourceType }]);
                  let channelTourUri = resourceUri;
                  this.channelTours.push(channelTourUri);
              }
          });

      let statusesSubscription = this.resourceService.getAllStatuses().subscribe(statuses => {
        statuses.forEach(status => {
          let idAsString = status.Id.toString();
          let resource = this.modelStore.get(idAsString);
          if (!resource)//it might not be an input channel
          {
            return;
          }
          let changed: boolean = (resource.status !== status.State);
          if (!changed)
          {
            return;
          }
          resource.status = status.State;

          this.resourceService.update(resource);


              });
              let filteredCameras = this.filterCameras(this.modelStore);
              this.filteredCameras = _.cloneDeep(filteredCameras);
              this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
              this.updateCameraCount();
          });
          this.subscriptions.push(statusesSubscription);
      });
      this.subscriptions.push(resourcesSubscription);
  }

  private showCamera(camera:Resource): boolean {
    if(!this.showBooleanCameras[0].show && !this.showBooleanCameras[1].show){
      return false;
    }
    if((camera.status === ResourceState.online ? true : false) === this.showBooleanCameras[0].show){
      return true;
    }
    else if((camera.status !== ResourceState.online ? true : false) === this.showBooleanCameras[1].show){
      return true;
    }
    return false;
  }

  toggleFilterButtons(index: number):void {
    this.showBooleanCameras.forEach((item, i) => {
      item.show = index === i;
    });

    this.filteredCameras = this.filterCameras(this.modelStore);
    this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
  }

  filterCameras(cameras:Map<string, Resource>):Map<string, Resource> {
    let filteredCameras: Map<string, Resource> = new Map<string, Resource>();

    cameras.forEach(resource => {
      if (resource.resourceType === ServerTypes.Core_RES_InputChannel) {
        const isOnline = resource.status === ResourceState.online;
        const showOnline = this.showBooleanCameras[0].show && isOnline;
        const showOffline = this.showBooleanCameras[1].show && !isOnline;

        if (showOnline || showOffline) {
          filteredCameras.set(resource.identity, resource);
        }
      }
    });

    return filteredCameras;
  }

  onCameraSelect(channel:Resource): void {
    if(this.selectedChannelId === channel.identity){
      this.selectedChannelId = null;
      this.onChannelSelect.emit({playerId: this.playerId, channelId: null, widgetId: this.selectedWidgetId});
      return;
    }
    this.selectedChannel = channel;
    if(this.sidebarAutoClose){
      this.videoWallService.selectedChannelId.next({"channelId":this.selectedChannel.identity,"playerId":this.playerId, "monitorIndex":this.queryParam});
      const cameraOptions: CameraSelectionOptions = {
        channelId: this.selectedChannel.identity,
        playerId: this.playerId
        // Uses defaults: monitorIndex: 0, updateLocalStorage: true
      };
      this.playerService.openChannel(cameraOptions);
      this.cancelAction();
    }
    this.onChannelSelect.emit({playerId: this.playerId, channelId: this.selectedChannel.identity, widgetId: this.selectedWidgetId});
  }

  cancelAction(): void {
    this.cymsidebarService.closeSidebar();
    this.playerId = null;
  }

  onDragStartCamera(channel: Resource): void {
    this.selectedChannel = channel;
    if (this.selectedChannel.status !== 'online'){
      return;
    }
    this.playerService.setDragAndDropChannel(this.selectedChannel.identity);
  }

  buildVideoDeviceGroup(resources: Map<string, Resource>): VideoDeviceGroup[]{
    let array: VideoDeviceGroup[] = [];
    for(let key in resources){
      let resource = resources.get(key);
      resource.groups.forEach((group:ResourceGroup) => {
        let index = array.findIndex(el => { return el.identity === group.identity; });
        if(index === -1){
          let videoDeviceGroup = new VideoDeviceGroup(group);
          videoDeviceGroup.videoDevices = {};
          videoDeviceGroup.videoDevices[resource.identity] = resource;
          array.push(videoDeviceGroup);
        }
        else {
          array[index].videoDevices[resource.identity] = resource;
        }
      });
    }
    return array;
  }

  setGroupsCounter(): void {
    this.groupsCounter = {};
    this.cameraElements.forEach(element => {
        let parentId = element.nativeElement.parentElement.id;
        if(!this.groupsCounter[parentId]){
          this.groupsCounter[parentId] = 1;
        }
        else {
          this.groupsCounter[parentId]++;
        }
    });
  }

  private updateOrCreateCameras(resources: Resource[]): void{
    resources.forEach(resource => {
      //TODO
      //This check should be deleted when api filtering is implemented
      if(resource.resourceType !== ServerTypes.Core_RES_InputChannel){
        return;
      }
      this.modelStore.set(resource.identity, resource);
      let filteredCameras = this.filterCameras(this.modelStore);
      this.filteredCameras = _.cloneDeep(filteredCameras);
      this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
    });
  }

  private deleteCameras(resources: Resource[]): void{
    resources.forEach(resource => {
      //TODO
      //This check should be deleted when api filtering is implemented
      if(resource.resourceType !== ServerTypes.Core_RES_InputChannel){
        return;
      }
      this.modelStore.delete(resource.identity);
      let filteredCameras = this.filterCameras(this.modelStore);
      this.filteredCameras = _.cloneDeep(filteredCameras);
      this.groupedCameras = this.buildVideoDeviceGroup(this.filteredCameras);
    });
  }

  getLayoutForMonitor(index){
    this.monitorIndex=index;
    return this.playerIndexChanged.emit(index);
  }

  saveLayout(){

    this.videoWallService.saveLayoutForMonitor({"MonitorId":this.queryParam-1,"VideoWallId":this.videoWallGuid,"LayoutId":this.selectedLayout}).subscribe(res=>{
      this.messageService.add({ severity: 'success', summary: this.i18n.instant(ToastTypes.success), detail: this.i18n.instant('layoutSelected') });
    });
  }

  layoutChange(event){
    this.viewLayout.emit(this.selectedLayout);
  }

  makeVideoLoop(entries?,playerId?,playerMap?:PlayerComponent){

    if(playerMap != null){
      this.playerIdOn.set(playerMap.playerId,false);
    }
    if(playerMap){
      let activePlayer=this.playerIdOn.get(playerMap.playerId);
      if(!activePlayer){
        return;
      }
    }

    if(playerId){

      if (this.playerIdOn.size === 0){
        this.channelTourArray.splice(0);
        return;
      }

      let activePlayer=this.playerIdOn.get(playerId);
      if(!activePlayer){
        let player=this.playerService.findPlayer(playerId);
        this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
        return;
      }
    }

    let entryTime=0;
    entries.map((entry,index) => {

        entryTime+=entry.Duration * 1000;
        let dataEntry:DataEntry={
          "ChannelId":entry.ChannelURI[entry.ChannelURI.length-1].id,
          "Duration":entryTime,
        }
        this.channelTourArray[index] = dataEntry;

        if(index == 0){
          const cameraOptions: CameraSelectionOptions = {
            channelId: entry.ChannelURI[entry.ChannelURI.length-1].id,
            playerId: playerId,
            isChannelTour: true
            // Uses defaults: monitorIndex: 0, updateLocalStorage: true
          };
          this.playerService.openChannel(cameraOptions);
        }

        if(index > 0){
          setTimeout(() =>{
            if (this.playerIdOn.size === 0){
              this.channelTourArray.splice(0);
              return;
            }

            let activePlayer=this.playerIdOn.get(playerId);
            if(!activePlayer){
              let player=this.playerService.findPlayer(playerId);
              this.playerService.ChannelClosed.next({ playerId: playerId, channelId: player.channelId });
              return;
            }
            const cameraOptions: CameraSelectionOptions = {
              channelId: entry.ChannelURI[entry.ChannelURI.length-1].id,
              playerId: playerId,
              isChannelTour: true
              // Uses defaults: monitorIndex: 0, updateLocalStorage: true
            };
            this.playerService.openChannel(cameraOptions);
            if(index === entries.length-1){
              setTimeout(() =>{
                this.makeVideoLoop(entries,playerId);
              },entryTime);

            }
          },entryTime);
        }
    })
      let player=this.playerService.findPlayer(playerId);
      if (player){
        this.playerService.setChannelClosed(player,true);
        let channelTourString=JSON.stringify(this.channelTourArray);
        this.playerService.setChannelTour(playerId, channelTourString);
      }
  }

 
  //TODO: make resource strongly typed
  //TODO this needs to be called from signalr handler
  async notifyOperation(resource: any): Promise<void>{

    let resourcesWithGroups = this.filterResourcesWithGroups(resource.models);
    let resourcesWithoutGroups = this.filterResourcesWithoutGroups(resource.models);

    switch(resource.type) {
        case DataChangeType.Update:
          if (resourcesWithoutGroups.length > 0){
            this.updateCameras(resourcesWithoutGroups);
          }

          if (resourcesWithGroups.length > 0){
            this.updateOrCreateCameras(resourcesWithGroups);
          }
          break;
        case DataChangeType.Create:
          if (resourcesWithGroups.length > 0) {
            this.updateOrCreateCameras(resourcesWithGroups);
          }
          break;
        case DataChangeType.Delete:
          this.deleteCameras(resource.models);
          break;
        default:
          console.error("operation type is not supported: ", resource.type);
          break;
     }
  }

  private updateCameras(resources: Resource[]): void {

    resources.forEach((resource: Resource) => {
      let cameraToUpdate = this.modelStore.get(resource.identity);

      if (cameraToUpdate)
      {
        cameraToUpdate.status = resource.status;
        let filteredCameras = this.filterCameras(this.modelStore);
        this.filteredCameras = _.cloneDeep(filteredCameras);
        this.updateCameraCount();
      }
    })
  }

  private filterResourcesWithGroups(resources: Resource[]): Resource[] {

    let resourcesWithGroups: Resource[] = [];

    resources.forEach((resource: Resource) => {
      if (resource.groups) {
        resourcesWithGroups.push(resource);
      }
    });

    return resourcesWithGroups;
  }

  private filterResourcesWithoutGroups(resources: Resource[]): Resource[] {

    let resourcesWithoutGroups: Resource[] = [];

    resources.forEach((resource: Resource) => {
      if (resource.identity && !resource.groups) {
        if (!resource.status)
        {
          resource.status = ResourceState.unknown;
        }

        resourcesWithoutGroups.push(resource);
      }
    });

    return resourcesWithoutGroups;
  }

  private updateCameraCount(): void {
    // Count cameras from the full modelStore
    const cameras = Array.from(this.modelStore.values())
      .filter(resource => resource.resourceType === ServerTypes.Core_RES_InputChannel);
    
    this.cameraCountService.updateCameraCountsFromResources(cameras);
  }
}
