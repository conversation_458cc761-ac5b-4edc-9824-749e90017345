export interface CameraSelectionOptions {
    // Required properties
    channelId: string;
    playerId: string;

    // Optional properties with sensible defaults
    monitorIndex?: number;           // Defaults to 0 for VMS, set for video wall
    updateLocalStorage?: boolean;    // Defaults to true, false for video wall
    isChannelTour?: boolean;         // Defaults to false
    isVideoWall?: boolean;           // Defaults to false
    widgetId?: string;               // Only for dashboard widgets
}