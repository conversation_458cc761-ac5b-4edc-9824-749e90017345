import { Store } from '@ngrx/store';
import { Component, Input, OnInit, OnDestroy, ViewChild } from '@angular/core';
import * as fromAppReducers from 'app/store/app.reducers';
import { PlayersService } from 'app/shared/services/players.service';
import { TreeDataComponent } from 'app/shared/components/tree-table/tree-table.component';
import { CameraSelectionOptions } from 'app/shared/models/camera-selection.model';

@Component({
    selector: 'app-open-video-channel',
    templateUrl: './open-video-channel.component.html',
    styleUrls: ['./open-video-channel.component.scss']
})
export class OpenVideoChannelComponent implements OnInit, OnDestroy {
    @Input('PlayerId') playerId = null;
    @ViewChild('treeTableComp', {static:false}) treeTableComp: TreeDataComponent;
    @Input('elemnetToAppend') elemnetToAppend = "body";
    private storeObservable;
    openAddModal = false;
    channelsList = [];
    channelSelected;
    treeTableColumns = [
        { field: 'name', header: 'name' },
        { field: 'state', header: 'status' }
    ];

    constructor(
        private store: Store<fromAppReducers.AppState>,
        private playerService: PlayersService
    ) { }

    ngOnInit() {
        this.storeObservable = this.store.select(fromAppReducers.StoreNames.getUserResourceGroupsWithChannel).subscribe(res => {
            if (res != null && res.length > 0) {
                this.channelsList = res[1];
            }
        });

        /* 
        old implementation
        Commented out due to opening of dialog when clicking on a player
        
        this.playerService.openVideoChannelDlg.subscribe(playerId => {
            this.playerId = playerId;
            this.openDialog();
        });
        */
    }

    openDialog() {
        this.treeTableComp.closeAllOpenNodes();
        this.openAddModal = true;
    }

    onCancel() {
        this.openAddModal = false;
    }

    onOpenChannel() {
        if (!this.channelSelected.state)
        return;
        
        this.openAddModal = false;
        const cameraOptions: CameraSelectionOptions = {
            channelId: this.channelSelected.id,
            playerId: this.playerId
            // Uses defaults: monitorIndex: 0, updateLocalStorage: true
        };
        this.playerService.openChannel(cameraOptions);
    }

    ngOnDestroy() {
        if (this.storeObservable) {
            this.storeObservable.unsubscribe();
        }
    }
}