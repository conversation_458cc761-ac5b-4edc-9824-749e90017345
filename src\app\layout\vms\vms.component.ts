import {
    AfterViewInit,
    Component,
    ComponentFactoryResolver,
    ElementRef,
    OnDestroy,
    OnInit,
    QueryList,
    ViewChild,
    ViewChildren,
    ViewContainerRef,
    ComponentRef
} from "@angular/core";
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { AppModal } from 'app/shared/components/app-modal/app-modal.component';
import { CymsidebarService } from "app/shared/components/cym-sidebar/cym-sidebar.service";
import { PlayerComponent } from "app/shared/components/player/player.component";
import { ToastTypes } from 'app/shared/enum/toast-types';
import { PlayerModel } from "app/shared/models/player.model";
import { CameraSelectionOptions } from "../../shared/models/camera-selection.model";
import {
    NavigationService,
    Pages
} from "app/shared/services/navigation.service";
import { SettingsService } from "app/shared/services/settings.service";
import * as fromAppReducers from "app/store/app.reducers";
import { ConfirmationService, MessageService, SelectItemGroup } from "primeng/api";
import { ResourceStoreService } from "../../services/store_servcies/resource-store.service";
import { FullScreenService } from "../../shared/services/fullscreen.service";
import { PlayersService } from "../../shared/services/players.service";
import { AppModalPosition } from "../../shared/components/app-modal/app-modal-position";

@Component({
    selector: "vms",
    templateUrl: "./vms.component.html",
    entryComponents: [PlayerComponent],
    styleUrls: ["./vms.component.scss"],
    providers: [CymsidebarService, ConfirmationService]
})
export class VMSComponent implements OnInit, OnDestroy, AfterViewInit {
    storeObservable;
    selectedCamera: string;
    selectedPlayerId: string;
    @ViewChildren("container") containers: QueryList<ElementRef>;
    groupedCameras: SelectItemGroup[];

    pComponents: PlayerComponent[] = [];
    players: ComponentRef<PlayerComponent>[] = [];
    playerCount = 0;
    playerCountNew = 1;
    type = "vms";
    layouts = [
        { label: "icon-view-1", value: 1 },
        { label: "icon-view-4", value: 4 },
        { label: "icon-view-9", value: 9 },
        { label: "icon-view-16", value: 16 }
    ];
    @ViewChild("sideBar", {static: false}) sideBar;
    @ViewChild("timeline", {static: false}) timeline;
    @ViewChild("playersWrapper", {static: false}) playersWrapper;
    @ViewChild("timelineWrapper", {static: false}) timelineWrapper;
    @ViewChild("modal", {static: false}) modal: AppModal;
    @ViewChild("layoutsModal", {static: false}) layoutsModal: AppModal;
    @ViewChild("camerasModal", {static: false}) camerasModal: AppModal;
    @ViewChild("reopenCamerasModal", {static: false}) reopenCamerasModal: AppModal;
    isTimelineOpen: boolean = false;
    playersJson = {};
    public opened: boolean = false;
    public barsEnabled: boolean = true;
    public dockedSize: string = "0px";
    public isSidebarDocked: boolean = true;
    public isMainSidebarOpen: boolean = true;
    public isChecked: boolean = false;
    vcaOptions = { showConfig: true, showObjects: true };
    showVCAConfig = true;
    showVCAObjects = true;
    ptzOptions = { show: true };
    showPTZControls = false;
    private playerId:string;
    private position : AppModalPosition = AppModalPosition.TopRight;

    constructor(
        private playersService: PlayersService,
        private viewContainer: ViewContainerRef,
        private componentFactoryResolver: ComponentFactoryResolver,
        private settingsService: SettingsService,
        private navigationService: NavigationService,
        private fullScreenService: FullScreenService,
        private cymsidebarService: CymsidebarService,
        private i18n: TranslateService,
        private store: Store<fromAppReducers.AppState>,
        private messageService: MessageService,
        private resourceStoreService: ResourceStoreService,
    ) { }

    ngOnInit(): void {
        let selected = this.navigationService.getSelectedOrDefault(this.type);
        if (selected[this.type]) {
            this.playerCountNew = selected[this.type];
        }

        // Initialize playerId for video channels
        this.playersService.playersInitialized.subscribe(playerIds => {
            if (playerIds && playerIds.length > 0 && playerIds[0]) {
                const id = playerIds[0].toString();
                if (id) {
                    this.playerId = id;
                }
            }
        });

        // Load existing players
        this.playersService.loadPlayers();

        this.isTimelineOpen = this.settingsService.get("isTimelineOpen");
        if (!this.isTimelineOpen) {
            this.isTimelineOpen = false;
        }

        this.playersService.ChannelOpend.subscribe(res => {
            this.playersJson[res.playerId] = res.playerId;
            this.controlTimeline();
        });

        this.playersService.ChannelClosed.subscribe(res => {
            delete this.playersJson[res.playerId];
            this.controlTimeline();
        });

        this.playersService.openVideoChannelDlg.subscribe(playerId => {
            this.playerId = playerId;

            this.camerasModal.openModal();
        });

        this.fullScreenService.fullScreenCall$.subscribe(isFullScreen => {
            if (isFullScreen) {
                this.isTimelineOpen = false;
            } else {
                this.isTimelineOpen = this.settingsService.get(
                    "isTimelineOpen"
                );
            }
        });

        let vcaOptions = this.settingsService.get("vcaOptions");
        if (vcaOptions) {
            this.showVCAConfig = vcaOptions.showConfig;
            this.showVCAObjects = vcaOptions.showObjects;
        }

        let ptzOptions = this.settingsService.get("ptzOptions");
        if (ptzOptions) {
            this.showPTZControls = ptzOptions.show;
        }
    }

    openTimeline(): void {
        this.isTimelineOpen = !this.isTimelineOpen;
        this.settingsService.set("isTimelineOpen", this.isTimelineOpen);
    }

    openLayouts(): void{
        this.layoutsModal.openModal();
    }

    controlTimeline(): void {
        if (
            this.isTimelineOpen &&
            this.timeline &&
            Object.keys(this.playersJson).length === 0
        ) {
            this.timeline._timeline.destroy();
            this.timeline.initTimeline();
        }
    }
    updateComponents(): void {
        if (this.playerCountNew === this.playerCount) { return; }
        if (this.playerCountNew > this.playerCount) {
            for (let i = this.playerCount; i < this.playerCountNew; i++) {
                this.addPlayer(i);
            }
        } else if (this.playerCountNew < this.playerCount) {
            for (let i = this.playerCount - 1; i > this.playerCountNew - 1; i--) {
                const player = this.players[i];
                if (player) {
                    player.destroy();
                    this.players.splice(i, 1);
                }
                const pComponent = this.pComponents[i];
                if (pComponent) {
                    pComponent.disconnect();
                    this.pComponents.splice(i, 1);
                }
            }
        }
        this.setPlayersInContainers();
        this.playerCount = this.playerCountNew;
    }

    setPlayersInContainers(): void {
        for (let i = 0; i < this.playerCountNew; i++) {
            const container = this.containers.toArray()[i];
            const player = this.players[i];
            if (container && player) {
                container.nativeElement.append(player.location.nativeElement);
            }
        }
    }

    addPlayer(index: number): void {
        let factory = this.componentFactoryResolver.resolveComponentFactory(PlayerComponent);
        let compRef = this.viewContainer.createComponent<PlayerComponent>(factory);
        compRef.instance.playerIndex = "v_" + index;
        compRef.instance.playerId = "v_" + index;
        compRef.instance.showPlayerActions = true;
        compRef.instance.onContainerClick.subscribe(() => this.onContainerClick(compRef.instance.playerId));
        this.players.push(compRef);
        this.pComponents.push(compRef.instance);
        compRef.changeDetectorRef.detectChanges();
    }

    onContainerClick(playerId: string): void {
        this.selectedPlayerId = playerId;
        if (this.camerasModal) {
            this.camerasModal.openModal();
        }
    }

    public ngOnDestroy(): void {
        this.playersService.clear();
        this.messageService.clear('vmsOpen');
    }

    ngAfterViewInit(): void {

        if (!this.containers.dirty) {
            this.initData();
        }
        //update on change
        this.containers.changes.subscribe(() => {
            this.initData();
        });
    }

    reopenCameras(): void {
        this.playersService.loadPlayers();
        this.messageService.clear('vmsOpen');
    }

    onVideoChannelSelect(cameraOptions: CameraSelectionOptions): void {
        if (cameraOptions.channelId) {
            const targetPlayerId = cameraOptions.playerId || this.selectedPlayerId;
            const finalCameraOptions: CameraSelectionOptions = {
                ...cameraOptions,
                playerId: targetPlayerId
            };
            this.playersService.openChannel(finalCameraOptions);
            if (this.camerasModal) {
                this.camerasModal.closeModal();
            }
        }
    }

    // Keep the old method for backward compatibility if needed elsewhere
    onChannelSelect(event: {playerId: string, channelId: string}): void {
        if (event.channelId) {
            const targetPlayerId = event.playerId || this.selectedPlayerId;
            const cameraOptions: CameraSelectionOptions = {
                channelId: event.channelId,
                playerId: targetPlayerId
            };
            this.playersService.openChannel(cameraOptions);
            if (this.camerasModal) {
                this.camerasModal.closeModal();
            }
        }
    }

    onClose(): void {
        this.messageService.clear('vmsOpen');
    }

    initData(): void {
        this.updateComponents();
       let playerModel:PlayerModel[]=this.transformFromPlayerComponentToPlayerMode(this.pComponents);
        this.playersService.setPlayersRef(playerModel);
    }

    transformFromPlayerComponentToPlayerMode(playerComponents:PlayerComponent[]):PlayerModel[]
    {
        let playerMode = [];
        for (let i = 0; i < playerComponents.length; i++) {
            playerMode.push(new PlayerModel(playerComponents[i].playerId,playerComponents[i].playerIndex, playerComponents[i].channelId,playerComponents[i].channelName,playerComponents[i].isActive,playerComponents[i].showSpinner));
        }

        return playerMode;
    }

    onLayoutChanged(event: {label: string, value: number}): void {
        this.navigationService.navigate(Pages.vms, { vms: event.value });
        if(event.value > this.playerCount){
            this.playerCountNew = event.value;
            setTimeout(() => {
              this.playersService.loadPlayers();
            }, 500);
        } else {
         this.playerCountNew = event.value;
        }
    }

    openSideBar(): void {
        this.cymsidebarService.toggleDockSidebar("push");
        this.cymsidebarService.openSidebar();
    }

    closeAllPlayers(): void {
        this.pComponents.forEach(component => {component.closeChannel();});
    }

    isCloseAllDisabled(): boolean {
        return Object.keys(this.playersJson).length === 0;
    }

    public show(): void {
        this.modal.openModal();
    }

    openVCAoptions(): void {
        this.modal.openModal();
    }

    onConfirm(): void {
        this.setVCAOptions();
        this.setPTZOptions();
        this.notifiy();
        this.modal.closeModal();
    }

    notifiy(): void {
        this.resourceStoreService.tryGetUserResourceGroups([{
            vcaOptions: this.vcaOptions,
            ptzOptions: this.ptzOptions
        }]);
    }
    onCancel(): void {
        this.modal.closeModal();
    }

    setVCAOptions(): void {
        this.vcaOptions = {
            showConfig: this.showVCAConfig,
            showObjects: this.showVCAObjects
        };
        this.settingsService.set("vcaOptions", this.vcaOptions);
    }

    setPTZOptions(): void {
        this.ptzOptions = {
            show: this.showPTZControls
        };
        this.settingsService.set("ptzOptions", this.ptzOptions);
    }


    togglePTZControls(): void {
        this.setPTZOptions();
        this.notifiy();
    }
}
