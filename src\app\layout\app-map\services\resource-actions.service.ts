import { ComponentF<PERSON>y<PERSON><PERSON>olver, ComponentRef, Inject, Injectable, Optional, QueryList, ViewContainerRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'app/shared/components/header/notifications/notifications.service';
import { ActionResultStatus } from 'app/shared/enum/action-result-status.enum';
import { ResourceActionType } from 'app/shared/enum/resource-action-type.enum';
import { ToastTypes } from 'app/shared/enum/toast-types';
import { ResourceEditorComponents } from 'app/shared/models/resource-editor-components';
import { ActionResult } from 'app/shared/modules/data-layer/models/action-result';
import { AuthService } from 'app/shared/services/auth.service';
import { FactorCompensationService } from 'app/shared/services/factor-compensation.service';
import { NavigationService, Pages } from 'app/shared/services/navigation.service';
import { PlayersService } from 'app/shared/services/players.service';
import { MessageService } from 'primeng/api';
import { Observable } from 'rxjs';
import { MapUtilsService } from './map-utils.service';
import {EventServiceNew} from "app/shared/modules/data-layer/services/event/eventServiceNew";
import { ResourceService } from 'app/services/resource/resource.service';

@Injectable({
  providedIn: 'root'
})

export class ResourceActionsService {
  componentRef: ComponentRef<any>;
  public ON_ACTION_FORM="onFormAction";
  constructor(
    private authService: AuthService,
    private navigationService: NavigationService,
    private playerService: PlayersService,
    private resourceService: ResourceService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private eventServiceNew:EventServiceNew,
    private eventService:EventServiceNew,
    private componentFactoryResolver: ComponentFactoryResolver,
    private mapUtilsService: MapUtilsService,
    private notificationsService: NotificationsService,
    private factorCompensationService:FactorCompensationService,
    @Inject('instanceId') @Optional() public instanceId?: string,
  ) {
  }

  executeAction(actionType: ResourceActionType, identity: string, name?: string, index?: number, viewContainerRefTargets?: QueryList<ViewContainerRef>,resource?): void {
    switch (actionType) {
      case ResourceActionType.jumpResourceToInventory:
        this.navigationService.navigate(Pages.inventory, {identity: identity});
        break;
      case ResourceActionType.jumpGroupToInventory:
        this.navigationService.navigate(Pages.inventory, {groups: name});
        break;
      case ResourceActionType.dimGroupLight:

        this.createDynamicComponent(actionType, {resourceId: identity, isGroup: true, userId: this.authService.user.Identity, targets: viewContainerRefTargets, index: index });
        break;
      case ResourceActionType.dimResourceLight:
        this.createDynamicComponent(actionType, {resourceId: identity, isGroup: false, userId: this.authService.user.Identity, targets: viewContainerRefTargets, index: index,resource:resource});
        break;
      case ResourceActionType.onOffButton:
        this.createDynamicComponent(actionType, {resourceId: identity, isGroup: false, userId: this.authService.user.Identity, targets: viewContainerRefTargets, index: index,resource:resource});
        break;
      case ResourceActionType.openVideoChannel:
        // Open video channel - will create popup container if no players available
        this.playerService.openChannelPlayback(identity, 1, null, null).subscribe(
          () => {
            // Channel opened successfully
          },
          (error) => {
            console.error('Error opening channel:', error);
            this.messageService.add({
              severity: 'error',
              summary: this.translateService.instant(ToastTypes.error),
              detail: this.translateService.instant('openChannelFail')
            });
          }
        );
        break;
      case ResourceActionType.ackEvent:
        this.ackEvent(identity);
        break;
      case ResourceActionType.ackAllEvents:
        this.ackEvent(null);
        break;
      case ResourceActionType.refreshData:
        this.refreshData(null, identity);
        break;
      case ResourceActionType.refreshPowerMeterData:
        this.refreshData(ResourceActionType.refreshPowerMeterData, identity);
        break;
      case ResourceActionType.showResourcesFromGroup:
        this.filterResourceGroup(identity);
        break;
      default:
        console.error('Action type not found: ', actionType)
        break;
    }
  }

  private createDynamicComponent(actionType: ResourceActionType, componentData): void {

    if (this.componentRef) {
      this.componentRef.destroy();
    }
    let container = componentData.targets.toArray();
    const factory = this.componentFactoryResolver.resolveComponentFactory(ResourceEditorComponents[actionType]);
    if(componentData.isGroup){
      this.componentRef = container[componentData.index].createComponent(factory);

    }else{
      this.componentRef = container[0].createComponent(factory);
    }
    if(componentData.resource){
      this.factorCompensationService.getWeatherFactorStatuses().subscribe(weatherResponse=>{
        weatherResponse.map(item=>{
          if(item.id === componentData.resource.identity){
            componentData.resource.weatherStatus=item.enabled;
          }
        })

        this.factorCompensationService.getTrafficFactorStatuses().subscribe(trafficResponse=>{
          trafficResponse.map(item=>{
            if(item.id === componentData.resource.identity){
             componentData.resource.trafficStatus=item.enabled;
            }
          })
          this.componentRef.instance.row=componentData.resource;

        })

      });
      this.componentRef.instance.row=componentData.resource;
    }
    this.componentRef.instance.data = componentData;

    if(this.componentRef.instance[this.ON_ACTION_FORM]){
      this.componentRef.instance[this.ON_ACTION_FORM].subscribe((data) => {
        if(data !== false) {
          this.resetComponentState();
        }
      });
    }
    this.componentRef.instance.data = componentData;


  }

  private resetComponentState() : void{
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }

  private filterResourceGroup(groupId): void {
    this.mapUtilsService.filterResourceGroupOnMapWithInstanceId(groupId, this.instanceId);
  }

  private refreshData(type?: ResourceActionType, identity?: string): void{
    let successMessage: string; let errorMessage: string; let failureMessage: string;
    switch (type) {
      case ResourceActionType.refreshPowerMeterData:
        successMessage = 'refreshPowerMeterDataSuccess';
        errorMessage = "refreshPowerMeterDataError";
        failureMessage = 'refreshPowerMeterDataFailure'
        break;
      default:
        successMessage = 'refreshLightDataSuccess';
        errorMessage = "refreshLightDataError";
        failureMessage = 'refreshLightDataFailure'
        break;
    }
    this.resourceService.refreshData(this.authService.user.Identity, identity).subscribe((res: ActionResult) => {
      switch(res.Status) {
        case ActionResultStatus.Success:
          this.messageService.add({
            severity: 'success', summary: this.translateService.instant(ToastTypes.success),
            detail: this.translateService.instant(successMessage)
          });
        break;
        case ActionResultStatus.Failure:
          this.messageService.add({
            severity: 'error', summary: this.translateService.instant(ToastTypes.error),
            detail: this.translateService.instant(failureMessage)
          });
        break;
        default:
          console.error('Type not found');
        break;
      }
     },(error) => {
      this.messageService.add({
        severity: 'error', summary: this.translateService.instant(ToastTypes.error),
        detail: this.translateService.instant(errorMessage)
      });
    })
  }


  private ackEvent(eventId?: string): void{

    let notification = this.notificationsService.FindCachedNotification(eventId);

    let observable: Observable<void>;
    if (eventId) {
      observable = this.eventServiceNew.ackEvent(this.authService.user.Identity, notification);
    }
    else {
      observable = this.eventServiceNew.ackAllEvents(this.authService.user.Identity)
    }
    observable.subscribe(_ => {
      this.messageService.add({ severity: 'success', summary: this.translateService.instant(ToastTypes.success), detail: this.translateService.instant('ackEventSuccess') });
    }, (error) => {
      this.messageService.add({ severity: 'error', summary: this.translateService.instant(ToastTypes.error), detail: this.translateService.instant('ackEventError') });
    })
  }

}
